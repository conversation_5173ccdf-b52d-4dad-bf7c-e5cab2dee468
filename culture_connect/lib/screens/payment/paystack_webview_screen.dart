import 'dart:async';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Production-ready Paystack WebView checkout screen
///
/// This screen provides a secure, enterprise-grade WebView implementation
/// for Paystack payments with comprehensive error handling and user experience
class PaystackWebViewScreen extends StatefulWidget {
  final String authorizationUrl;
  final String transactionReference;
  final double amount;
  final String currency;
  final VoidCallback? onSuccess;
  final VoidCallback? onCancel;
  final Function(String)? onError;

  const PaystackWebViewScreen({
    super.key,
    required this.authorizationUrl,
    required this.transactionReference,
    required this.amount,
    required this.currency,
    this.onSuccess,
    this.onCancel,
    this.onError,
  });

  @override
  State<PaystackWebViewScreen> createState() => _PaystackWebViewScreenState();
}

class _PaystackWebViewScreenState extends State<PaystackWebViewScreen>
    with TickerProviderStateMixin {
  final LoggingService _loggingService = LoggingService();
  late AnimationController _loadingController;
  late final WebViewController _webViewController;
  // Animation removed to eliminate unused variable warning

  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  Timer? _timeoutTimer;

  // Payment completion tracking
  bool _paymentCompleted = false;
  bool _paymentCancelled = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeWebView();
    _startPaymentTimeout();
    _logPaymentInitiation();
  }

  @override
  void dispose() {
    _loadingController.dispose();
    _timeoutTimer?.cancel();
    super.dispose();
  }

  void _initializeAnimations() {
    _loadingController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    // Animation setup removed to eliminate unused variable

    _loadingController.repeat();
  }

  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
              _hasError = false;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
            _checkPaymentCompletion(url);
          },
          onWebResourceError: (WebResourceError error) {
            setState(() {
              _hasError = true;
              _errorMessage =
                  'Failed to load payment page: ${error.description}';
              _isLoading = false;
            });
            _loggingService.error(
              'PaystackWebViewScreen',
              'WebView error: ${error.description}',
              {'errorCode': error.errorCode},
            );
          },
          onNavigationRequest: (NavigationRequest request) {
            // Allow all navigation for payment flow
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.authorizationUrl));
  }

  void _checkPaymentCompletion(String url) {
    // Check for Paystack success/failure URLs
    if (url.contains('success') ||
        url.contains('callback') && url.contains('status=success')) {
      if (!_paymentCompleted) {
        _handlePaymentSuccess();
      }
    } else if (url.contains('cancel') ||
        url.contains('failed') ||
        url.contains('status=failed')) {
      if (!_paymentCancelled) {
        _handlePaymentCancel();
      }
    }

    // Log URL for debugging
    _loggingService.debug(
      'PaystackWebViewScreen',
      'Navigation to URL: $url',
      {'transactionReference': widget.transactionReference},
    );
  }

  void _startPaymentTimeout() {
    // Set a 10-minute timeout for payment completion
    _timeoutTimer = Timer(const Duration(minutes: 10), () {
      if (!_paymentCompleted && !_paymentCancelled && mounted) {
        _handlePaymentTimeout();
      }
    });
  }

  void _logPaymentInitiation() {
    _loggingService.info(
      'PaystackWebViewScreen',
      'Payment WebView initiated',
      {
        'transactionReference': widget.transactionReference,
        'amount': widget.amount,
        'currency': widget.currency,
        // Don't log full authorization URL for security
        'hasAuthorizationUrl': widget.authorizationUrl.isNotEmpty,
      },
    );
  }

  void _handlePaymentTimeout() {
    setState(() {
      _hasError = true;
      _errorMessage = 'Payment session timed out. Please try again.';
    });

    _loggingService.warning(
      'PaystackWebViewScreen',
      'Payment timeout occurred',
      {'transactionReference': widget.transactionReference},
    );

    widget.onError?.call('Payment timeout');
  }

  void _handlePaymentSuccess() {
    if (_paymentCompleted) return;

    setState(() {
      _paymentCompleted = true;
      _isLoading = false;
    });

    _loggingService.info(
      'PaystackWebViewScreen',
      'Payment completed successfully',
      {'transactionReference': widget.transactionReference},
    );

    // Trigger achievement and mascot celebration
    _triggerPaymentCelebration();

    widget.onSuccess?.call();
  }

  void _handlePaymentCancel() {
    if (_paymentCancelled) return;

    setState(() {
      _paymentCancelled = true;
      _isLoading = false;
    });

    _loggingService.info(
      'PaystackWebViewScreen',
      'Payment cancelled by user',
      {'transactionReference': widget.transactionReference},
    );

    widget.onCancel?.call();
  }

  // _handlePaymentError method removed as it was unused

  Future<void> _triggerPaymentCelebration() async {
    try {
      // TODO: Integrate with Achievement and Mascot services when available
      // This will be handled by the parent integration service
      _loggingService.info(
        'PaystackWebViewScreen',
        'Payment celebration triggered',
        {'transactionReference': widget.transactionReference},
      );
    } catch (e) {
      _loggingService.warning(
        'PaystackWebViewScreen',
        'Failed to trigger payment celebration',
        {'error': e.toString()},
      );
    }
  }

  // These methods will be used when actual WebView is implemented
  // Currently kept for future WebView integration

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: const Text('Complete Payment'),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            _handlePaymentCancel();
            Navigator.of(context).pop();
          },
        ),
        actions: [
          if (_isLoading)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.primary,
                  ),
                ),
              ),
            ),
        ],
      ),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    if (_hasError) {
      return _buildErrorView(context);
    }

    return Stack(
      children: [
        _buildWebView(),
        if (_isLoading) _buildLoadingOverlay(context),
      ],
    );
  }

  Widget _buildWebView() {
    return WebViewWidget(controller: _webViewController);
  }

  Widget _buildLoadingOverlay(BuildContext context) {
    return Container(
      color: Colors.black.withAlpha(128),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'Loading secure payment...',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Payment Error',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.error,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage ?? 'An unexpected error occurred',
              style: theme.textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        ),
      ),
    );
  }
}
